<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Streaming Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/contrib/auto-render.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Streaming Test Page</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Real-time Streaming</h2>
            <button id="testBtn" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Test Streaming
            </button>
            <button id="clearBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded ml-2">
                Clear
            </button>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4">Streaming Output:</h3>
            <div id="streamingOutput" class="border rounded p-4 min-h-[200px] bg-gray-50 overflow-y-auto max-h-[400px]">
                <div class="text-gray-500 italic">Click "Test Streaming" to see real-time text streaming...</div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-semibold mb-4">Formatted Output:</h3>
            <div id="formattedOutput" class="border rounded p-4 min-h-[200px] bg-gray-50 overflow-y-auto max-h-[400px]">
                <div class="text-gray-500 italic">Formatted content will appear here after streaming completes...</div>
            </div>
        </div>
    </div>

    <script>
        // Simulate the streaming functionality
        async function testStreaming() {
            const streamingOutput = document.getElementById('streamingOutput');
            const formattedOutput = document.getElementById('formattedOutput');
            const testBtn = document.getElementById('testBtn');
            
            // Disable button during test
            testBtn.disabled = true;
            testBtn.textContent = 'Streaming...';
            
            // Clear previous content
            streamingOutput.innerHTML = '<div class="streaming-content whitespace-pre-wrap font-mono text-sm"></div>';
            formattedOutput.innerHTML = '<div class="text-gray-500 italic">Streaming in progress...</div>';
            
            const streamingDiv = streamingOutput.querySelector('.streaming-content');
            
            // Sample text that simulates LLM response
            const sampleText = `# Key Concept
The concept being tested here is related to chemical bonding and molecular structure.

# Why Option A is Correct
The correct answer demonstrates understanding of:
• Intermolecular forces and their relative strengths
• The relationship between molecular structure and physical properties
• How hydrogen bonding affects boiling points

The key insight is that hydrogen bonding creates stronger intermolecular attractions compared to van der Waals forces.

Mathematical relationship: $E_{bond} = k \\frac{q_1 q_2}{r}$

For display math:
$$\\Delta H = \\sum \\text{bonds broken} - \\sum \\text{bonds formed}$$

This explains why compounds with hydrogen bonding have higher boiling points.`;

            let currentText = '';
            
            // Simulate character-by-character streaming
            for (let i = 0; i < sampleText.length; i++) {
                currentText += sampleText[i];
                streamingDiv.textContent = currentText;
                streamingOutput.scrollTop = streamingOutput.scrollHeight;
                
                // Random delay to simulate network streaming
                await new Promise(resolve => setTimeout(resolve, Math.random() * 50 + 10));
            }
            
            // After streaming is complete, apply formatting
            setTimeout(() => {
                let formattedText = currentText
                    // Add proper styling to headings
                    .replace(/^# (.*?)$/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-4 mb-2">$1</h3>')
                    .replace(/^## (.*?)$/gm, '<h4 class="text-md font-semibold text-gray-800 mt-3 mb-2">$1</h4>')
                    .replace(/^### (.*?)$/gm, '<h5 class="text-sm font-semibold text-gray-800 mt-2 mb-1">$1</h5>')
                    // Style bullet points with • symbol
                    .replace(/^• (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    // Style numbered lists
                    .replace(/^\\d+\\. (.*?)$/gm, '<li class="ml-4 list-decimal list-inside mb-1">$1</li>')
                    // Style traditional markdown lists (fallback)
                    .replace(/^\\* (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    .replace(/^- (.*?)$/gm, '<li class="ml-4 list-disc list-inside mb-1">$1</li>')
                    // Remove any remaining markdown bold/italic formatting and replace with proper HTML
                    .replace(/\\*\\*(.*?)\\*\\*/g, '<strong class="font-semibold">$1</strong>')
                    .replace(/\\*(.*?)\\*/g, '<em class="italic">$1</em>')
                    // Handle code blocks
                    .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm">$1</code>')
                    // Wrap paragraphs
                    .replace(/^(?!<h|<li|<p|<div|<strong|<em|<code)(.*?)$/gm, '<p class="mb-2 leading-relaxed">$1</p>');

                formattedOutput.innerHTML = formattedText;
                
                // Render LaTeX
                if (typeof renderMathInElement !== 'undefined') {
                    renderMathInElement(formattedOutput, {
                        delimiters: [
                            {left: '$$', right: '$$', display: true},
                            {left: '$', right: '$', display: false},
                            {left: '\\\\(', right: '\\\\)', display: false},
                            {left: '\\\\[', right: '\\\\]', display: true}
                        ],
                        throwOnError: false,
                        output: 'html'
                    });
                }
                
                // Re-enable button
                testBtn.disabled = false;
                testBtn.textContent = 'Test Streaming';
            }, 500);
        }
        
        function clearOutput() {
            document.getElementById('streamingOutput').innerHTML = '<div class="text-gray-500 italic">Click "Test Streaming" to see real-time text streaming...</div>';
            document.getElementById('formattedOutput').innerHTML = '<div class="text-gray-500 italic">Formatted content will appear here after streaming completes...</div>';
        }
        
        document.getElementById('testBtn').addEventListener('click', testStreaming);
        document.getElementById('clearBtn').addEventListener('click', clearOutput);
    </script>
</body>
</html>
