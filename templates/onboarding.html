{% extends "base.html" %}

{% block content %}
<style>
/* Custom styling for confidence sliders */
.confidence-slider {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #4f46e5 0%, #4f46e5 50%, #e5e7eb 50%, #e5e7eb 100%);
    outline: none;
}

.confidence-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.confidence-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4f46e5;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Hover effects for cards */
.grade-card:hover, .subject-card:hover {
    transform: translateY(-2px);
}

/* Selected state animations */
.grade-card, .subject-card {
    transition: all 0.2s ease-in-out;
}
</style>
<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            {% if is_restart %}
                <h1 class="text-4xl font-bold tracking-tight text-gray-900 mb-2">Update Your Preferences</h1>
                <p class="text-lg text-gray-600">Let's update your subject and level preferences</p>
            {% else %}
                <h1 class="text-4xl font-bold tracking-tight text-gray-900 mb-2">Welcome to Vast!</h1>
                <p class="text-lg text-gray-600">Let's personalize your learning experience</p>
            {% endif %}
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex items-center justify-center">
                <div class="flex items-center space-x-4">
                    <!-- Step 1: Grade Level -->
                    <div class="flex items-center">
                        <div id="step1-circle" class="w-10 h-10 rounded-full bg-indigo-600 text-white flex items-center justify-center font-semibold text-sm">
                            1
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-900">Grade Level</span>
                    </div>

                    <!-- Connector -->
                    <div id="connector1" class="w-16 h-1 bg-gray-200 rounded"></div>

                    <!-- Step 2: Subjects -->
                    <div class="flex items-center">
                        <div id="step2-circle" class="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-semibold text-sm">
                            2
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Subjects</span>
                    </div>

                    <!-- Connector -->
                    <div id="connector2" class="w-16 h-1 bg-gray-200 rounded"></div>

                    <!-- Step 3: Confidence -->
                    <div class="flex items-center">
                        <div id="step3-circle" class="w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-semibold text-sm">
                            3
                        </div>
                        <span class="ml-2 text-sm font-medium text-gray-500">Confidence</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onboarding Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <form id="onboarding-form" method="POST" action="{{ url_for('complete_onboarding') }}">

                <!-- Step 1: Grade Level Selection -->
                <div id="step1" class="step-content">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">What's your current grade level?</h2>
                        <p class="text-gray-600">This helps us recommend the right content for you</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                        <!-- PSLE Option -->
                        <label class="grade-option cursor-pointer">
                            <input type="radio" name="grade_level" value="PSLE" class="sr-only grade-radio">
                            <div class="grade-card border-2 border-gray-200 rounded-xl p-6 text-center hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                                <div class="text-4xl mb-4">📚</div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">PSLE</h3>
                                <p class="text-gray-600 text-sm">Primary School Leaving Examination</p>
                            </div>
                        </label>

                        <!-- J1 Option -->
                        <label class="grade-option cursor-pointer">
                            <input type="radio" name="grade_level" value="J1" class="sr-only grade-radio">
                            <div class="grade-card border-2 border-gray-200 rounded-xl p-6 text-center hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                                <div class="text-4xl mb-4">🎓</div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">J1</h3>
                                <p class="text-gray-600 text-sm">Junior College Year 1</p>
                            </div>
                        </label>

                        <!-- J2 Option -->
                        <label class="grade-option cursor-pointer">
                            <input type="radio" name="grade_level" value="J2" class="sr-only grade-radio">
                            <div class="grade-card border-2 border-gray-200 rounded-xl p-6 text-center hover:border-indigo-300 hover:shadow-lg transition-all duration-200">
                                <div class="text-4xl mb-4">🏆</div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">J2</h3>
                                <p class="text-gray-600 text-sm">Junior College Year 2</p>
                            </div>
                        </label>
                    </div>

                    <div class="flex justify-end mt-8">
                        <button type="button" id="step1-next" class="px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Next: Choose Subjects
                        </button>
                    </div>
                </div>

                <!-- Step 2: Subject Selection -->
                <div id="step2" class="step-content hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Which subjects are you taking?</h2>
                        <p class="text-gray-600">Select all the subjects you're currently studying</p>
                    </div>

                    <div id="subjects-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
                        <!-- Subjects will be populated by JavaScript based on grade level -->
                    </div>

                    <div class="flex justify-between mt-8">
                        <button type="button" id="step2-back" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition-colors duration-200">
                            Back
                        </button>
                        <button type="button" id="step2-next" class="px-6 py-3 bg-indigo-600 text-white rounded-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Next: Set Confidence
                        </button>
                    </div>
                </div>

                <!-- Step 3: Confidence Level -->
                <div id="step3" class="step-content hidden">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">How confident are you in each subject?</h2>
                        <p class="text-gray-600">Rate your confidence level from 1 (beginner) to 5 (expert)</p>
                    </div>

                    <div id="confidence-container" class="space-y-6 max-w-2xl mx-auto">
                        <!-- Confidence sliders will be populated by JavaScript -->
                    </div>

                    <div class="flex justify-between mt-8">
                        <button type="button" id="step3-back" class="px-6 py-3 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition-colors duration-200">
                            Back
                        </button>
                        <button type="submit" id="complete-onboarding" class="px-6 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200">
                            Complete Setup
                        </button>
                    </div>
                </div>

                <!-- Hidden inputs for form submission -->
                <input type="hidden" name="subjects_taken" id="subjects_taken_input">
                <input type="hidden" name="subject_confidence" id="subject_confidence_input">
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    let selectedGrade = '';
    let selectedSubjects = [];
    let subjectConfidence = {};

    // Subject options based on grade level
    const subjectOptions = {
        'PSLE': [
            { id: 'psle-math', name: 'Mathematics', icon: '🔢' },
            { id: 'psle-science', name: 'Science', icon: '🔬' },
            { id: 'psle-english', name: 'English', icon: '📖' },
            { id: 'psle-chinese', name: 'Chinese', icon: '🇨🇳' }
        ],
        'J1': [
            { id: 'h2-physics', name: 'H2 Physics', icon: '⚛️' },
            { id: 'h2-chemistry', name: 'H2 Chemistry', icon: '🧪' },
            { id: 'h2-mathematics', name: 'H2 Mathematics', icon: '📐' },
            { id: 'h1-physics', name: 'H1 Physics', icon: '🌟' },
            { id: 'h1-chemistry', name: 'H1 Chemistry', icon: '⚗️' },
            { id: 'h1-mathematics', name: 'H1 Mathematics', icon: '📊' }
        ],
        'J2': [
            { id: 'h2-physics', name: 'H2 Physics', icon: '⚛️' },
            { id: 'h2-chemistry', name: 'H2 Chemistry', icon: '🧪' },
            { id: 'h2-mathematics', name: 'H2 Mathematics', icon: '📐' },
            { id: 'h1-physics', name: 'H1 Physics', icon: '🌟' },
            { id: 'h1-chemistry', name: 'H1 Chemistry', icon: '⚗️' },
            { id: 'h1-mathematics', name: 'H1 Mathematics', icon: '📊' }
        ]
    };

    // Grade selection handlers
    document.querySelectorAll('.grade-radio').forEach(radio => {
        radio.addEventListener('change', function() {
            selectedGrade = this.value;

            // Update UI
            document.querySelectorAll('.grade-card').forEach(card => {
                card.classList.remove('border-indigo-500', 'bg-indigo-50');
                card.classList.add('border-gray-200');
            });

            this.parentElement.querySelector('.grade-card').classList.remove('border-gray-200');
            this.parentElement.querySelector('.grade-card').classList.add('border-indigo-500', 'bg-indigo-50');

            // Enable next button
            document.getElementById('step1-next').disabled = false;
        });
    });

    // Step navigation
    document.getElementById('step1-next').addEventListener('click', function() {
        if (selectedGrade) {
            showStep(2);
            populateSubjects();
        }
    });

    document.getElementById('step2-back').addEventListener('click', function() {
        showStep(1);
    });

    document.getElementById('step2-next').addEventListener('click', function() {
        if (selectedSubjects.length > 0) {
            showStep(3);
            populateConfidenceSliders();
        }
    });

    document.getElementById('step3-back').addEventListener('click', function() {
        showStep(2);
    });

    function showStep(step) {
        // Hide all steps
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show current step
        document.getElementById(`step${step}`).classList.remove('hidden');

        // Update progress indicators
        updateProgressIndicators(step);
        currentStep = step;
    }

    function updateProgressIndicators(step) {
        for (let i = 1; i <= 3; i++) {
            const circle = document.getElementById(`step${i}-circle`);
            const text = circle.parentElement.querySelector('span');

            if (i < step) {
                // Completed step
                circle.className = 'w-10 h-10 rounded-full bg-green-500 text-white flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = '✓';
                text.className = 'ml-2 text-sm font-medium text-green-600';
            } else if (i === step) {
                // Current step
                circle.className = 'w-10 h-10 rounded-full bg-indigo-600 text-white flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = i;
                text.className = 'ml-2 text-sm font-medium text-gray-900';
            } else {
                // Future step
                circle.className = 'w-10 h-10 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-semibold text-sm';
                circle.innerHTML = i;
                text.className = 'ml-2 text-sm font-medium text-gray-500';
            }
        }

        // Update connectors
        for (let i = 1; i <= 2; i++) {
            const connector = document.getElementById(`connector${i}`);
            if (step > i) {
                connector.className = 'w-16 h-1 bg-green-500 rounded';
            } else {
                connector.className = 'w-16 h-1 bg-gray-200 rounded';
            }
        }
    }

    function populateSubjects() {
        const container = document.getElementById('subjects-container');
        container.innerHTML = '';

        const subjects = subjectOptions[selectedGrade] || [];

        subjects.forEach(subject => {
            const subjectCard = document.createElement('label');
            subjectCard.className = 'subject-option cursor-pointer';
            subjectCard.innerHTML = `
                <input type="checkbox" value="${subject.id}" class="sr-only subject-checkbox">
                <div class="subject-card border-2 border-gray-200 rounded-xl p-4 text-center hover:border-indigo-300 hover:shadow-md transition-all duration-200">
                    <div class="text-3xl mb-2">${subject.icon}</div>
                    <h3 class="text-lg font-semibold text-gray-900">${subject.name}</h3>
                </div>
            `;

            const checkbox = subjectCard.querySelector('.subject-checkbox');
            checkbox.addEventListener('change', function() {
                const card = this.parentElement.querySelector('.subject-card');

                if (this.checked) {
                    selectedSubjects.push(this.value);
                    card.classList.remove('border-gray-200');
                    card.classList.add('border-indigo-500', 'bg-indigo-50');
                } else {
                    selectedSubjects = selectedSubjects.filter(s => s !== this.value);
                    card.classList.remove('border-indigo-500', 'bg-indigo-50');
                    card.classList.add('border-gray-200');
                }

                // Enable/disable next button
                document.getElementById('step2-next').disabled = selectedSubjects.length === 0;
            });

            container.appendChild(subjectCard);
        });
    }

    function populateConfidenceSliders() {
        const container = document.getElementById('confidence-container');
        container.innerHTML = '';

        selectedSubjects.forEach(subjectId => {
            const subject = subjectOptions[selectedGrade].find(s => s.id === subjectId);
            if (!subject) return;

            const sliderContainer = document.createElement('div');
            sliderContainer.className = 'bg-gray-50 rounded-xl p-6';
            sliderContainer.innerHTML = `
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3">${subject.icon}</span>
                        <h3 class="text-lg font-semibold text-gray-900">${subject.name}</h3>
                    </div>
                    <span id="confidence-${subjectId}" class="text-lg font-bold text-indigo-600">3</span>
                </div>
                <div class="relative">
                    <input type="range" min="1" max="5" value="3"
                           class="confidence-slider w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                           data-subject="${subjectId}">
                    <div class="flex justify-between text-xs text-gray-500 mt-2">
                        <span>Beginner</span>
                        <span>Intermediate</span>
                        <span>Expert</span>
                    </div>
                </div>
            `;

            const slider = sliderContainer.querySelector('.confidence-slider');
            slider.addEventListener('input', function() {
                const value = this.value;
                subjectConfidence[subjectId] = parseInt(value);
                document.getElementById(`confidence-${subjectId}`).textContent = value;

                // Update slider color based on value
                const percentage = ((value - 1) / 4) * 100;
                this.style.background = `linear-gradient(to right, #4f46e5 0%, #4f46e5 ${percentage}%, #e5e7eb ${percentage}%, #e5e7eb 100%)`;
            });

            // Initialize confidence value
            subjectConfidence[subjectId] = 3;

            container.appendChild(sliderContainer);
        });
    }

    // Form submission
    document.getElementById('onboarding-form').addEventListener('submit', function(e) {
        // Populate hidden inputs
        document.getElementById('subjects_taken_input').value = JSON.stringify(selectedSubjects);
        document.getElementById('subject_confidence_input').value = JSON.stringify(subjectConfidence);
    });
});
</script>
{% endblock %}
