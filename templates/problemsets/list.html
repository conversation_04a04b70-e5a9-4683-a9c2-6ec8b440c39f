{% extends "base.html" %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto" id="problemsets-header-section">
            <h1 class="text-2xl font-semibold text-gray-900">Problem Sets</h1>
            <p class="mt-2 text-sm text-gray-700">A list of all problem sets you have created or have access to.</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <a href="{{ url_for('create_problemset') }}" id="create-problemset-button" class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">
                Create Problem Set
            </a>
        </div>
    </div>

    <!-- Your Problem Sets -->
    <div class="mt-8" id="owned-problemsets-section">
        <h2 class="text-lg font-medium text-gray-900">Your Problem Sets</h2>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {% for problemset in owned_problemsets %}
            <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
                <div>
                    <span class="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-700 ring-4 ring-white">
                        <i class="fas fa-book-open"></i>
                    </span>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-medium">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="focus:outline-none">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            {{ problemset.name }}
                        </a>
                    </h3>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ problemset.description or 'No description provided' }}
                    </p>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <span class="text-sm text-gray-500">
                        {{ problemset.questions|length }} questions
                    </span>
                    <div class="flex items-center space-x-2">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('problemset_submissions', id=problemset.id) }}" class="text-green-600 hover:text-green-800" title="View Submissions">
                            <i class="fas fa-history"></i>
                        </a>
                        <a href="{{ url_for('edit_problemset', id=problemset.id) }}" class="text-yellow-600 hover:text-yellow-800">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteProblemset({{ problemset.id }})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-span-full">
                <p class="text-gray-500 text-center py-4">You haven't created any problem sets yet.</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Shared Problem Sets -->
    <div class="mt-12" id="shared-problemsets-section">
        <h2 class="text-lg font-medium text-gray-900">Shared With You</h2>
        <div class="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {% for problemset in shared_problemsets %}
            <div class="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow hover:shadow-md transition-shadow duration-200">
                <div>
                    <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                        <i class="fas fa-share-alt"></i>
                    </span>
                </div>
                <div class="mt-4">
                    <h3 class="text-lg font-medium">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="focus:outline-none">
                            <span class="absolute inset-0" aria-hidden="true"></span>
                            {{ problemset.name }}
                        </a>
                    </h3>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ problemset.description or 'No description provided' }}
                    </p>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <span class="text-sm text-gray-500">
                        {{ problemset.questions|length }} questions
                    </span>
                    <span class="text-sm text-gray-500">
                        Shared by {{ problemset.creator.username }}
                    </span>
                    <div class="flex items-center space-x-2">
                        <a href="{{ url_for('view_problemset', id=problemset.id) }}" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('problemset_submissions', id=problemset.id) }}" class="text-green-600 hover:text-green-800" title="View Submissions">
                            <i class="fas fa-history"></i>
                        </a>
                        <a href="{{ url_for('edit_problemset', id=problemset.id) }}" class="text-yellow-600 hover:text-yellow-800">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteProblemset({{ problemset.id }})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-span-full">
                <p class="text-gray-500 text-center py-4">No problem sets have been shared with you.</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const isLoggedIn = {{ session.get('user_id') is not none | tojson }};
    const problemsetsHeader = document.getElementById('problemsets-header-section');
    const createProblemsetButton = document.getElementById('create-problemset-button');
    const ownedProblemsetsSection = document.getElementById('owned-problemsets-section');
    const sharedProblemsetsSection = document.getElementById('shared-problemsets-section');

    let isContinuation = false;
    if (localStorage.getItem('startProblemsetsTourNext') === 'true') {
        localStorage.removeItem('startProblemsetsTourNext'); // Consume the signal
        localStorage.removeItem('problemsetsShepherdTourCompleted'); // Ensure this tour runs
        isContinuation = true;
    }

    const tourCompleted = localStorage.getItem('problemsetsShepherdTourCompleted');

    // Start tour if logged in AND (it's a continuation OR tour hasn't been completed)
    // AND the main header element exists.
    if (isLoggedIn && (isContinuation || !tourCompleted) && problemsetsHeader) {
        const tour = new Shepherd.Tour({
            useModalOverlay: true,
            defaultStepOptions: {
                classes: 'bg-white rounded-lg shadow-xl border border-gray-300 text-gray-700', // Updated classes
                scrollTo: { behavior: 'smooth', block: 'center' }
            }
        });

        tour.addStep({
            id: 'problemsets-page-intro',
            text: 'This is the Problem Sets page. Here you can manage your own problem sets and access sets shared with you or your groups.',
            attachTo: { element: problemsetsHeader, on: 'bottom' },
            buttons: [
                {
                    action() { return this.next(); },
                    text: 'Next',
                    classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                },
                {
                    action() {
                        localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                        return this.complete();
                    },
                    text: 'Skip Tour',
                    classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                }
            ]
        });

        if (ownedProblemsetsSection) {
            tour.addStep({
                id: 'owned-problemsets-info',
                text: 'Problem sets you create will appear in this "Your Problem Sets" section.',
                attachTo: { element: ownedProblemsetsSection, on: 'bottom' },
                buttons: [
                    {
                        action() { return this.back(); },
                        text: 'Back',
                        classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                    },
                    {
                        action() { return this.next(); },
                        text: 'Next',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() {
                            localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });
        }

        if (sharedProblemsetsSection) {
            tour.addStep({
                id: 'shared-problemsets-info',
                text: 'Problem sets shared with you or your groups will be listed here.',
                attachTo: { element: sharedProblemsetsSection, on: 'top' },
                buttons: [
                    {
                        action() { return this.back(); },
                        text: 'Back',
                        classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                    },
                    {
                        action() { return this.next(); },
                        text: 'Next',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    },
                    {
                        action() {
                            localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Skip Tour',
                        classes: 'px-4 py-2 bg-transparent text-gray-600 text-sm font-semibold rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 ml-2'
                    }
                ]
            });
        }

        if (createProblemsetButton) {
            tour.addStep({
                id: 'create-problemset-action',
                text: 'You can create a new problem set by clicking this button.',
                attachTo: { element: createProblemsetButton, on: 'bottom' },
                buttons: [
                    {
                        action() { return this.back(); },
                        text: 'Back',
                        classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                    },
                    {
                        action() {
                            localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                            return this.complete();
                        },
                        text: 'Finish Tour',
                        classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                    }
                ]
            });
        } else {
            // If create button isn't there for some reason, make the last available step the final one.
            const lastStep = tour.steps[tour.steps.length - 1];
            if (lastStep) {
                lastStep.updateOptions({
                    buttons: [
                        {
                            action() { return this.back(); },
                            text: 'Back',
                            classes: 'px-4 py-2 bg-gray-200 text-gray-700 text-sm font-semibold rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2'
                        },
                        {
                            action() {
                                localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
                                return this.complete();
                            },
                            text: 'Finish Tour',
                            classes: 'px-4 py-2 bg-indigo-600 text-white text-sm font-semibold rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                        }
                    ]
                });
            }
        }

        tour.on('complete', function() {
            localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
        });
        tour.on('cancel', function() {
            localStorage.setItem('problemsetsShepherdTourCompleted', 'true'); // Also mark as completed on cancel
        });

        tour.start();
    } else if (isLoggedIn && isContinuation && !problemsetsHeader) {
        // If it's a continuation but the main header isn't found, mark as completed.
        localStorage.setItem('problemsetsShepherdTourCompleted', 'true');
    }
});
</script>
{% endblock %} 
