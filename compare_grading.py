#!/usr/bin/env python3
"""
Script to compare current grading method vs new LLM-based rubric grading
"""

import json
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
from app import app
from models import db, Question, Part, Topic, Subject, MarkingPoint, Submission
import google.generativeai as genai
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GradingTimer:
    """Timer class for tracking grading performance"""
    def __init__(self):
        self.start_time = time.time()
        self.steps = []
        self.current_step_start = None
        self.current_step_name = None

    def start_step(self, step_name):
        if self.current_step_start is not None:
            self.end_current_step()

        self.current_step_name = step_name
        self.current_step_start = time.time()

    def end_current_step(self):
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None
            self.current_step_name = None

    def get_summary(self):
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

def _process_single_marking_point(mp_data, mp_index, user_answer, part_data, marking_points_data, app_logger, assigned_border_class):
    """Process a single marking point and return the evaluation result."""
    point_score = 0
    is_correct_mp = False
    is_partial_mp = False
    evidence_mp = None
    error_mp = False

    try:
        # Generate LLM prompt
        prompt = f"""\
            You are an expert examiner evaluating a student's answer against a specific marking point.
            TASK:
            1. Determine if the student's answer demonstrates understanding of the marking point
            2. Classify the answer and provide structured feedback in one of these formats:
               - If FULLY CORRECT: "Correctly identified <concept>"
               - If the student attempted to address the concept but was INCORRECT: "Incorrecty explained <concept"
               - If the student OMITTED the concept entirely: "Omitted <concept>"
            3. For FULLY or PARTIALLY correct answers, identify the exact text from the student's answer that provides evidence

            RESPONSE FORMAT:
            You must respond in one of these four formats ONLY:

            Format 1 - If the marking point is FULLY addressed:
            YES
            EVIDENCE: <exact text from student's answer>

            Format 2 - If the marking point was PARTIALLY addressed
            PARTIAL
            EVIDENCE: <exact text from student's answer>

            Format 3 - If the marking point is NOT addressed
            NO

            IMPORTANT: Do not include any other text, explanations, or formatting in your response.

            MARKING POINT: {mp_data['description']}
            OTHER MARKING POINTS (exclude these from your evaluation): {', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
            STUDENT'S ANSWER: {user_answer}
            """

        # Call LLM
        generation_config = {
            "temperature": 0.3, "top_p": 0.95, "top_k": 40, "max_output_tokens": 4096,
        }
        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
        ]
        gemini_model = genai.GenerativeModel('gemini-2.5-flash')
        response_text = gemini_model.generate_content(
            prompt,
            generation_config=generation_config,
            safety_settings=safety_settings
        ).text.strip()

        # Process response
        response_upper = response_text.upper()
        is_correct_mp = 'YES' in response_upper and not 'PARTIAL' in response_upper
        is_partial_mp = 'PARTIAL' in response_upper

        app_logger.debug(f"LLM Response for marking point {mp_data['id']}: {response_text}")

        # Extract evidence for correct/partial answers
        if is_correct_mp or is_partial_mp:
            if 'EVIDENCE:' in response_text:
                evidence_parts = response_text.split('EVIDENCE:', 1)
                if len(evidence_parts) > 1:
                    evidence_mp = evidence_parts[1].strip()
            elif 'EVIDENCE' in response_text: # Fallback if colon is missing
                evidence_parts = response_text.split('EVIDENCE', 1)
                if len(evidence_parts) > 1:
                    evidence_mp = evidence_parts[1].lstrip(':').strip()

        # Calculate score
        if is_correct_mp:
            point_score = mp_data['score']
        elif is_partial_mp:
            point_score = mp_data['score'] * 0.5

    except Exception as e_mp:
        app_logger.exception(f"Error evaluating marking point {mp_data['id']} with LLM: {str(e_mp)}")
        error_mp = True

    # Return result
    return {
        'id': mp_data['id'],
        'description': mp_data['description'],
        'score': mp_data['score'],
        'achieved': is_correct_mp,
        'partial': is_partial_mp,
        'achieved_score': point_score,
        'evidence': evidence_mp,
        'feedback': mp_data['description'],
        'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_mp else None,
        'error': error_mp,
        'mp_index': mp_index
    }

def _calculate_score_and_evaluated_points(user_answer: str, part_data: Part, gemini_model, app_logger):
    """
    Calculates the score for a given part and returns evaluated marking points.
    This logic is shared between get_git_diff and submit_problemset.
    """
    # Initialize timing tracker
    timer = GradingTimer()
    timer.start_step("Initialization and Setup")

    total_score = 0
    evaluated_points = []

    if part_data.input_type == 'mcq':
        timer.start_step("MCQ Processing")
        # For MCQ, the answer is the selected option index (passed as user_answer)
        if not user_answer: # Should be validated before calling this helper
            timer.start_step("MCQ Error - No Answer")
            timing_summary = timer.get_summary()
            return {'score': 0, 'evaluated_points': [], 'error': 'No answer provided for MCQ', 'timing': timing_summary}

        try:
            timer.start_step("MCQ Validation")
            selected_option_index = int(user_answer)
            options = part_data.options

            if not options or selected_option_index >= len(options) or selected_option_index < 0:
                timer.start_step("MCQ Error - Invalid Option")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'error': 'Invalid option selected for MCQ', 'timing': timing_summary}

            timer.start_step("MCQ Scoring")
            selected_option = options[selected_option_index]
            is_correct = selected_option.is_correct # Assuming is_correct is a boolean field
            total_score = part_data.score if is_correct else 0

            timer.start_step("MCQ Feedback Generation")
            # For MCQs, create structured feedback based on correctness
            if is_correct:
                feedback_text = f"Correctly identified {selected_option.description}"
            else:
                # Find the correct option for feedback
                correct_option = next((opt for opt in options if opt.is_correct), None)
                if correct_option:
                    feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                else:
                    feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

            timer.start_step("MCQ Result Compilation")
            evaluated_points.append({
                'id': f"mcq_{part_data.id}",
                'description': f"Selected option: {selected_option.description}",
                'score': part_data.score,
                'achieved': is_correct,
                'partial': False,
                'achieved_score': total_score,
                'evidence': str(selected_option_index),
                'feedback': feedback_text,
                'color': 'border-green-400' if is_correct else 'border-red-400'
            })

            timing_summary = timer.get_summary()
            return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

        except (ValueError, TypeError) as e:
            timer.start_step("MCQ Error - Exception")
            app_logger.exception(f"Error processing MCQ answer in helper: {str(e)}")
            timing_summary = timer.get_summary()
            return {'score': 0, 'evaluated_points': [], 'error': 'Invalid MCQ answer format', 'timing': timing_summary}

    # For non-MCQ questions (free response with marking points)
    timer.start_step("Free Response Setup")
    try:
        marking_points_data = [{
            'id': mp.id,
            'description': mp.description,
            'score': mp.score
        } for mp in part_data.marking_points]

        highlight_border_classes = [
            'border-yellow-400', 'border-blue-400', 'border-green-400',
            'border-pink-400', 'border-purple-400', 'border-indigo-400',
            'border-teal-400', 'border-orange-400', 'border-lime-400',
            'border-cyan-400'
        ]

        timer.start_step("Parallel Marking Point Processing")
        # Process marking points in parallel
        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all marking point evaluation tasks
            future_to_mp = {}
            for mp_index, mp_data in enumerate(marking_points_data):
                assigned_border_class = highlight_border_classes[mp_index % len(highlight_border_classes)]
                future = executor.submit(
                    _process_single_marking_point,
                    mp_data, mp_index, user_answer, part_data, marking_points_data,
                    app_logger, assigned_border_class
                )
                future_to_mp[future] = mp_data

            # Collect results as they complete
            results = [None] * len(marking_points_data)  # Pre-allocate list to maintain order
            for future in as_completed(future_to_mp):
                try:
                    result = future.result()
                    if result and 'mp_index' in result:
                        results[result['mp_index']] = result
                except Exception as e:
                    mp_data = future_to_mp[future]
                    app_logger.exception(f"Error processing marking point {mp_data['id']}: {str(e)}")

        timer.start_step("Result Compilation and Score Calculation")
        # Calculate total score and prepare evaluated points
        total_score = 0
        evaluated_points = []
        for result in results:
            if result:  # Ensure result is not None
                total_score += result['achieved_score']
                # Remove mp_index from result before adding to evaluated_points
                result_copy = result.copy()
                result_copy.pop('mp_index', None)
                evaluated_points.append(result_copy)

        timer.start_step("Final Score Compilation")
        timing_summary = timer.get_summary()
        return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

    except Exception as e:
        timer.start_step("Error Handling - Free Response")
        app_logger.exception(f"Error processing free-response answer in helper: {str(e)}")
        timing_summary = timer.get_summary()
        # Return 0 score and empty points if a major error occurs in this block
        return {'score': 0, 'evaluated_points': [], 'error': 'Error processing marking points', 'timing': timing_summary}

def load_rubrics():
    """Load the generated rubrics from JSON file"""
    try:
        with open('chemistry_rubrics.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ chemistry_rubrics.json not found. Please run generate_rubrics.py first.")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error loading rubrics JSON: {e}")
        return None

def grade_with_new_method(question_text, rubric, student_answer):
    """Grade using the new LLM-based rubric method"""
    
    prompt = f"""You are an objective grader. Your task is to evaluate a student's answer based on the provided examination question and the detailed objective rubric.

Strictly adhere to the rubric for scoring. Do not introduce external knowledge or subjective judgment.

---
Examination Question:
{question_text}

---
Objective Rubric:
{rubric}

---
Student's Answer:
{student_answer}

---
Please provide your grading output in JSON format with two keys:
1. `score`: An integer representing the total score obtained by the student.
2. `comments`: A string containing the point breakdown for each criterion and a brief explanation for the points awarded or deducted.

Example JSON output:
{{
  "score": 5,
  "comments": "Criterion 1: 2/2 points (correct definition). Criterion 2: 3/3 points (identified all items)."
}}"""

    try:
        model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
        response = model.generate_content(
            prompt,
            generation_config={
                'response_mime_type': 'application/json'
            }
        )
        
        result = json.loads(response.text)
        return {
            'score': result.get('score', 0),
            'comments': result.get('comments', ''),
            'success': True,
            'error': None
        }
    except Exception as e:
        return {
            'score': 0,
            'comments': '',
            'success': False,
            'error': str(e)
        }

def grade_with_current_method(part, student_answer):
    """Grade using the current marking points method"""
    try:
        # Initialize Gemini model (same as in current system)
        gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
        
        # Use the existing grading function
        grading_details = _calculate_score_and_evaluated_points(
            student_answer, part, gemini_model, logger
        )
        
        return {
            'score': grading_details.get('score', 0),
            'evaluated_points': grading_details.get('evaluated_points', []),
            'success': True,
            'error': grading_details.get('error', None),
            'timing': grading_details.get('timing', {})
        }
    except Exception as e:
        return {
            'score': 0,
            'evaluated_points': [],
            'success': False,
            'error': str(e),
            'timing': {}
        }

def get_student_submissions():
    """Get all student submissions for chemistry questions"""
    with app.app_context():
        # Find chemistry subject
        chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
        if not chemistry_subject:
            return []

        # Get submissions for chemistry questions
        submissions = db.session.query(Submission).join(Part).join(Question).join(Topic).filter(
            Topic.subject_id == chemistry_subject.id,
            Submission.answer.isnot(None),
            Submission.answer != ''
        ).limit(100).all()  # Increased limit for better testing

        return submissions

def process_single_submission(submission_data, rubrics_data):
    """Process a single submission with both grading methods"""
    submission_id, question_id, part_id, part_description, student_answer, max_score, original_score = submission_data

    # Check if we have a rubric for this question/part
    question_key = str(question_id)
    part_key = str(part_id)

    if question_key not in rubrics_data["rubrics"]:
        return {
            'submission_id': submission_id,
            'error': f'No rubric found for question {question_id}',
            'success': False
        }

    if part_key not in rubrics_data["rubrics"][question_key]["parts"]:
        return {
            'submission_id': submission_id,
            'error': f'No rubric found for part {part_id}',
            'success': False
        }

    rubric = rubrics_data["rubrics"][question_key]["parts"][part_key]["rubric"]

    # Get part object for current method (need to do this in app context)
    with app.app_context():
        part = Part.query.get(part_id)
        if not part:
            return {
                'submission_id': submission_id,
                'error': f'Part {part_id} not found in database',
                'success': False
            }

        # Test current method
        start_time = time.time()
        current_result = grade_with_current_method(part, student_answer)
        current_time = time.time() - start_time

        # Test new method
        start_time = time.time()
        new_result = grade_with_new_method(part_description, rubric, student_answer)
        new_time = time.time() - start_time

        # Prepare detailed feedback for Excel
        current_feedback = ""
        if current_result.get("evaluated_points"):
            feedback_parts = []
            for point in current_result["evaluated_points"]:
                status = "✓" if point.get("achieved") else "✗"
                score_text = f"{point.get('achieved_score', 0)}/{point.get('score', 0)}"
                feedback_parts.append(f"{status} {point.get('description', '')}: {score_text}")
            current_feedback = " | ".join(feedback_parts)

        return {
            'submission_id': submission_id,
            'question_id': question_id,
            'part_id': part_id,
            'question_title': part.question.title if part.question else 'Unknown',
            'part_description': part_description[:100] + "..." if len(part_description) > 100 else part_description,
            'max_score': max_score,
            'student_answer': student_answer[:300] + "..." if len(student_answer) > 300 else student_answer,
            'original_score': original_score,
            'current_score': current_result["score"],
            'current_success': current_result["success"],
            'current_error': current_result.get("error", ""),
            'current_time': current_time,
            'current_feedback': current_feedback,
            'new_score': new_result["score"],
            'new_success': new_result["success"],
            'new_error': new_result.get("error", ""),
            'new_time': new_time,
            'new_feedback': new_result.get("comments", ""),
            'score_difference': abs(current_result["score"] - new_result["score"]) if current_result["success"] and new_result["success"] else None,
            'success': True
        }

def compare_grading_methods():
    """Compare both grading methods on actual student submissions using parallel processing"""

    # Load rubrics
    rubrics_data = load_rubrics()
    if not rubrics_data:
        return

    # Get student submissions
    submissions = get_student_submissions()
    if not submissions:
        print("No student submissions found for chemistry questions")
        return

    print(f"🧪 Found {len(submissions)} chemistry submissions to test")
    print(f"📚 Using {len(rubrics_data['rubrics'])} available rubrics")
    print(f"⚡ Processing with parallel workers...")

    # Prepare submission data for parallel processing (avoid lazy loading issues)
    submission_data_list = []
    with app.app_context():
        for submission in submissions:
            # Eagerly load the part data to avoid session issues
            part = Part.query.get(submission.part_id)
            if part:
                submission_data_list.append((
                    submission.id,
                    submission.question_id,
                    submission.part_id,
                    part.description,
                    submission.answer,
                    part.score,
                    submission.score
                ))

    # Process submissions in parallel
    results = []
    successful_results = []

    with ThreadPoolExecutor(max_workers=5) as executor:
        # Submit all tasks
        future_to_submission = {
            executor.submit(process_single_submission, sub_data, rubrics_data): sub_data[0]
            for sub_data in submission_data_list
        }

        # Collect results as they complete
        for i, future in enumerate(as_completed(future_to_submission), 1):
            submission_id = future_to_submission[future]
            try:
                result = future.result()
                results.append(result)

                if result.get('success', False):
                    successful_results.append(result)
                    print(f"✅ {i}/{len(submissions)} - Submission {submission_id}: Current={result['current_score']:.1f}, New={result['new_score']:.1f}")
                else:
                    print(f"❌ {i}/{len(submissions)} - Submission {submission_id}: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ {i}/{len(submissions)} - Submission {submission_id}: Exception - {str(e)}")
                results.append({
                    'submission_id': submission_id,
                    'error': str(e),
                    'success': False
                })

    print(f"\n📊 Processing complete! {len(successful_results)}/{len(results)} submissions processed successfully")

    # Calculate summary statistics
    both_successful = 0
    current_only_successful = 0
    new_only_successful = 0
    both_failed = 0
    score_differences = []
    current_times = []
    new_times = []

    for result in successful_results:
        current_times.append(result['current_time'])
        new_times.append(result['new_time'])

        if result['current_success'] and result['new_success']:
            both_successful += 1
            if result['score_difference'] is not None:
                score_differences.append(result['score_difference'])
        elif result['current_success']:
            current_only_successful += 1
        elif result['new_success']:
            new_only_successful += 1
        else:
            both_failed += 1

    # Create Excel output
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f"grading_comparison_{timestamp}.xlsx"

    # Prepare data for Excel
    excel_data = []
    for result in results:
        if result.get('success', False):
            excel_data.append({
                'Submission_ID': result['submission_id'],
                'Question_ID': result['question_id'],
                'Part_ID': result['part_id'],
                'Question_Title': result['question_title'],
                'Part_Description': result['part_description'],
                'Max_Score': result['max_score'],
                'Student_Answer': result['student_answer'],
                'Original_Score': result['original_score'],
                'Current_Method_Score': result['current_score'],
                'Current_Method_Success': result['current_success'],
                'Current_Method_Error': result['current_error'],
                'Current_Method_Time_Seconds': round(result['current_time'], 3),
                'Current_Method_Feedback': result['current_feedback'],
                'New_Method_Score': result['new_score'],
                'New_Method_Success': result['new_success'],
                'New_Method_Error': result['new_error'],
                'New_Method_Time_Seconds': round(result['new_time'], 3),
                'New_Method_Feedback': result['new_feedback'],
                'Score_Difference': result['score_difference'],
                'Methods_Agree': result['current_score'] == result['new_score'] if result['current_success'] and result['new_success'] else False
            })
        else:
            excel_data.append({
                'Submission_ID': result['submission_id'],
                'Error': result.get('error', 'Unknown error'),
                'Success': False
            })

    # Create DataFrame and save to Excel
    df = pd.DataFrame(excel_data)

    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # Main results sheet
        df.to_excel(writer, sheet_name='Comparison_Results', index=False)

        # Summary statistics sheet
        summary_data = {
            'Metric': [
                'Total Submissions Tested',
                'Successfully Processed',
                'Both Methods Successful',
                'Current Method Only Successful',
                'New Method Only Successful',
                'Both Methods Failed',
                'Average Score Difference',
                'Average Current Method Time (s)',
                'Average New Method Time (s)',
                'Speed Improvement Factor'
            ],
            'Value': [
                len(results),
                len(successful_results),
                both_successful,
                current_only_successful,
                new_only_successful,
                both_failed,
                round(sum(score_differences) / len(score_differences), 3) if score_differences else 0,
                round(sum(current_times) / len(current_times), 3) if current_times else 0,
                round(sum(new_times) / len(new_times), 3) if new_times else 0,
                round((sum(current_times) / len(current_times)) / (sum(new_times) / len(new_times)), 2) if current_times and new_times else 0
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

    # Print summary
    print(f"\n🎉 Grading Method Comparison Complete!")
    print(f"=" * 60)
    print(f"📊 Results Summary:")
    print(f"   • Total submissions tested: {len(results)}")
    print(f"   • Successfully processed: {len(successful_results)}")
    print(f"   • Both methods successful: {both_successful}")
    print(f"   • Current method only: {current_only_successful}")
    print(f"   • New method only: {new_only_successful}")
    print(f"   • Both failed: {both_failed}")

    if score_differences:
        avg_diff = sum(score_differences) / len(score_differences)
        print(f"   • Average score difference: {avg_diff:.3f}")

    if current_times and new_times:
        avg_current = sum(current_times) / len(current_times)
        avg_new = sum(new_times) / len(new_times)
        speed_factor = avg_current / avg_new if avg_new > 0 else 0
        print(f"   • Average time - Current: {avg_current:.3f}s")
        print(f"   • Average time - New: {avg_new:.3f}s")
        print(f"   • Speed improvement: {speed_factor:.2f}x")

    print(f"\n📁 Detailed results saved to: {excel_filename}")
    print(f"   • Sheet 1: Comparison_Results (detailed data)")
    print(f"   • Sheet 2: Summary_Statistics (overview)")

    return excel_filename
if __name__ == "__main__":
    print("⚖️  Grading Method Comparison with Parallel Processing")
    print("=" * 60)
    compare_grading_methods()
