#!/usr/bin/env python3
"""
Test script for the intelligent review system
This script tests the core functionality without requiring authentication
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from routes.spaced_repetition import SpacedRepetitionEngine
from routes.performance_analysis import PerformanceAnalysisEngine
from routes.recommendation_engine import RecommendationEngine
from models import User, Submission, ReviewSchedule, PerformanceAnalytics

def test_spaced_repetition():
    """Test the spaced repetition algorithm"""
    print("Testing Spaced Repetition Algorithm...")
    
    # Test quality score calculation
    test_cases = [
        (100, 100, 5),  # Perfect score -> Quality 5
        (85, 100, 4),   # Good score -> Quality 4
        (65, 100, 3),   # Fair score -> Quality 3
        (45, 100, 2),   # Poor score -> Quality 2
        (25, 100, 1),   # Bad score -> Quality 1
        (0, 100, 0),    # Fail -> Quality 0
    ]
    
    for score, max_score, expected_quality in test_cases:
        quality = SpacedRepetitionEngine.calculate_quality_score(score, max_score)
        status = "✓" if quality == expected_quality else "✗"
        print(f"  {status} Score {score}/{max_score} -> Quality {quality} (expected {expected_quality})")
    
    # Test interval calculation
    print("\n  Testing interval calculations:")
    for quality in range(6):
        interval, ease = SpacedRepetitionEngine.calculate_next_interval(0, 2.5, quality)
        print(f"    Quality {quality}: Interval {interval} days, Ease {ease:.2f}")

def test_performance_analysis():
    """Test performance analysis functionality"""
    print("\nTesting Performance Analysis...")
    
    with app.app_context():
        # Get a user with submissions
        user = User.query.filter(User.id.in_(
            db.session.query(Submission.user_id).distinct()
        )).first()
        
        if not user:
            print("  ✗ No users with submissions found")
            return
        
        print(f"  Testing with user {user.id}")
        
        # Test performance analytics
        analytics = PerformanceAnalytics.query.filter_by(user_id=user.id).all()
        print(f"  ✓ Found {len(analytics)} performance analytics records")
        
        # Test learning insights
        try:
            insights = PerformanceAnalysisEngine.get_learning_insights(user.id)
            print(f"  ✓ Learning insights generated:")
            print(f"    - Weak areas: {len(insights.get('weak_areas', []))}")
            print(f"    - Strong areas: {len(insights.get('strong_areas', []))}")
            print(f"    - Overall success rate: {insights.get('overall_success_rate', 0):.1%}")
        except Exception as e:
            print(f"  ✗ Error generating insights: {e}")

def test_recommendations():
    """Test recommendation generation"""
    print("\nTesting Recommendation Engine...")
    
    with app.app_context():
        # Get a user with submissions
        user = User.query.filter(User.id.in_(
            db.session.query(Submission.user_id).distinct()
        )).first()
        
        if not user:
            print("  ✗ No users with submissions found")
            return
        
        print(f"  Testing with user {user.id}")
        
        try:
            # Test recommendation generation
            recommendations = RecommendationEngine.generate_recommendations(user.id, limit=10)
            print(f"  ✓ Generated {len(recommendations)} recommendations")
            
            # Show recommendation breakdown by type
            types = {}
            for rec in recommendations:
                rec_type = rec['type']
                types[rec_type] = types.get(rec_type, 0) + 1
            
            for rec_type, count in types.items():
                print(f"    - {rec_type}: {count}")
            
            # Show top 3 recommendations
            if recommendations:
                print("  Top 3 recommendations:")
                for i, rec in enumerate(recommendations[:3]):
                    print(f"    {i+1}. Question {rec['question_id']}, Part {rec['part_id']}")
                    print(f"       Type: {rec['type']}, Priority: {rec['priority_score']:.1f}")
                    print(f"       Reason: {rec['reason']}")
        
        except Exception as e:
            print(f"  ✗ Error generating recommendations: {e}")

def test_review_schedules():
    """Test review schedule functionality"""
    print("\nTesting Review Schedules...")
    
    with app.app_context():
        # Get review schedule statistics
        total_schedules = ReviewSchedule.query.count()
        print(f"  ✓ Total review schedules: {total_schedules}")
        
        # Get schedules by user
        user_schedules = db.session.query(
            ReviewSchedule.user_id,
            db.func.count(ReviewSchedule.id).label('count')
        ).group_by(ReviewSchedule.user_id).all()
        
        print("  Review schedules by user:")
        for user_id, count in user_schedules:
            print(f"    User {user_id}: {count} schedules")
        
        # Test review statistics for a user
        if user_schedules:
            test_user_id = user_schedules[0][0]
            try:
                stats = SpacedRepetitionEngine.get_review_statistics(test_user_id)
                print(f"  ✓ Review statistics for user {test_user_id}:")
                print(f"    - Total items: {stats.get('total_items', 0)}")
                print(f"    - Due today: {stats.get('due_today', 0)}")
                print(f"    - Overdue: {stats.get('overdue', 0)}")
                print(f"    - Due this week: {stats.get('due_this_week', 0)}")
            except Exception as e:
                print(f"  ✗ Error getting review statistics: {e}")

def main():
    """Run all tests"""
    print("=" * 60)
    print("INTELLIGENT REVIEW SYSTEM - FUNCTIONALITY TEST")
    print("=" * 60)
    
    test_spaced_repetition()
    test_performance_analysis()
    test_recommendations()
    test_review_schedules()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
