#!/usr/bin/env python3
"""
Test script for the teacher dashboard functionality
"""

import os

def test_files_exist():
    """Test that all required files exist"""
    required_files = [
        'routes/teacher.py',
        'templates/teacher_dashboard.html'
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print(f"✗ Missing files: {missing_files}")
        return False
    else:
        print("✓ All required files exist")
        return True

def test_teacher_routes_syntax():
    """Test that teacher routes file has valid Python syntax"""
    try:
        with open('routes/teacher.py', 'r') as f:
            content = f.read()

        # Check for key function definitions
        required_functions = [
            'def register_teacher_routes',
            'def get_submission_analytics',
            'def get_question_analytics'
        ]

        missing_functions = []
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)

        if missing_functions:
            print(f"✗ Missing functions in teacher.py: {missing_functions}")
            return False

        # Check for route definitions
        required_routes = [
            '@app.route("/teacher")',
            '@app.route("/api/teacher/analytics")',
            '@app.route("/api/teacher/topics/<int:subject_id>")',
            '@app.route("/api/teacher/question-analytics")'
        ]

        missing_routes = []
        for route in required_routes:
            if route not in content:
                missing_routes.append(route)

        if missing_routes:
            print(f"✗ Missing routes in teacher.py: {missing_routes}")
            return False

        print("✓ Teacher routes file contains all required functions and routes")
        return True

    except Exception as e:
        print(f"✗ Error checking teacher routes: {e}")
        return False

def test_template_exists():
    """Test that the teacher dashboard template exists"""
    template_path = os.path.join('templates', 'teacher_dashboard.html')
    if os.path.exists(template_path):
        print("✓ Teacher dashboard template exists")
        
        # Check for key elements in template
        with open(template_path, 'r') as f:
            content = f.read()
            
        required_elements = [
            'Teacher Dashboard',
            'apply-filters',
            'export-data',
            'analytics-table',
            'loadAnalytics',
            'exportToCSV'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"✗ Missing template elements: {missing_elements}")
            return False
        else:
            print("✓ Template contains all required elements")
            return True
    else:
        print("✗ Teacher dashboard template not found")
        return False

def test_navigation_links():
    """Test that navigation links are added to base template"""
    base_template_path = os.path.join('templates', 'base.html')
    if os.path.exists(base_template_path):
        with open(base_template_path, 'r') as f:
            content = f.read()
        
        # Check for teacher dashboard links
        if 'teacher_dashboard' in content and 'chalkboard-teacher' in content:
            print("✓ Teacher dashboard navigation links added")
            return True
        else:
            print("✗ Teacher dashboard navigation links not found")
            return False
    else:
        print("✗ Base template not found")
        return False

def main():
    """Run all tests"""
    print("Testing Teacher Dashboard Implementation")
    print("=" * 50)
    
    tests = [
        ("File Existence", test_files_exist),
        ("Teacher Routes Syntax", test_teacher_routes_syntax),
        ("Template Existence", test_template_exists),
        ("Navigation Links", test_navigation_links)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nTesting {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} failed")
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Teacher dashboard implementation is complete.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    main()
